{% extends "public/base_public.html" %}

{% block title %}{{ current_category.replace('-', ' ').title() }} - Amazon Affiliate Store{% endblock %}
{% block description %}Browse our selection of {{ current_category.replace('-', ' ') }} products on Amazon.{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Category Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('public.index') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('public.products') }}">Products</a></li>
                    <li class="breadcrumb-item active">{{ current_category.replace('-', ' ').title() }}</li>
                </ol>
            </nav>
            <h1>{{ current_category.replace('-', ' ').title() }}</h1>
            <p class="text-muted">Discover our curated selection of {{ current_category.replace('-', ' ') }} products</p>
        </div>
        <div class="col-md-4">
            <!-- Category Navigation -->
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                    Browse Other Categories
                </button>
                <ul class="dropdown-menu w-100">
                    {% for cat in categories %}
                        {% if cat != current_category %}
                            <li><a class="dropdown-item" href="{{ url_for('public.products_by_category', category=cat) }}">{{ cat.replace('-', ' ').title() }}</a></li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Products Grid -->
    {% if products.items %}
        <div class="row">
            {% for product in products.items %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card h-100 shadow-sm">
                    {% if product.image_url %}
                        <img src="{{ product.image_url }}" class="card-img-top" alt="{{ product.title }}" style="height: 200px; object-fit: cover;">
                    {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="bi bi-image text-muted display-4"></i>
                        </div>
                    {% endif %}
                    
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">{{ product.title[:60] }}{% if product.title|length > 60 %}...{% endif %}</h6>
                        <p class="card-text text-muted small flex-grow-1">
                            {{ product.description[:100] if product.description else 'No description available.' }}
                            {% if product.description and product.description|length > 100 %}...{% endif %}
                        </p>
                        
                        <div class="mt-auto">
                            {% if product.price %}
                                <div class="text-center mb-2">
                                    <span class="fw-bold text-success fs-5">{{ product.price }}</span>
                                </div>
                            {% endif %}
                            
                            <div class="d-grid gap-2">
                                <a href="{{ product.affiliate_link }}" class="btn btn-warning" target="_blank" rel="noopener">
                                    <i class="bi bi-cart-plus"></i> Buy on Amazon
                                </a>
                                <a href="{{ url_for('public.product_detail', id=product.id) }}" class="btn btn-outline-primary btn-sm">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if products.pages > 1 %}
        <nav aria-label="Category products pagination" class="mt-5">
            <ul class="pagination justify-content-center">
                {% if products.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('public.products_by_category', category=current_category, page=products.prev_num) }}">Previous</a>
                    </li>
                {% endif %}
                
                {% for page_num in products.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != products.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('public.products_by_category', category=current_category, page=page_num) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if products.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('public.products_by_category', category=current_category, page=products.next_num) }}">Next</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <!-- No Products Found -->
        <div class="text-center py-5">
            <i class="bi bi-box-seam display-1 text-muted"></i>
            <h3 class="mt-3">No products in this category yet</h3>
            <p class="text-muted">Check back later for new {{ current_category.replace('-', ' ') }} products.</p>
            <a href="{{ url_for('public.products') }}" class="btn btn-primary">
                <i class="bi bi-arrow-left"></i> Browse All Products
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
