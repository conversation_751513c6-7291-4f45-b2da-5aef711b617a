{% extends "admin/base_admin.html" %}

{% block title %}Products - Admin Panel{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Products</h1>
        <div>
            <a href="{{ url_for('admin.add_product') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Add Product
            </a>
            <a href="{{ url_for('admin.import_products') }}" class="btn btn-success">
                <i class="bi bi-upload"></i> Import CSV
            </a>
        </div>
    </div>
    
    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-6">
                    <input type="text" class="form-control" name="search" placeholder="Search products..." value="{{ search }}">
                </div>
                <div class="col-md-4">
                    <select class="form-select" name="category">
                        <option value="">All Categories</option>
                        <option value="electronics" {% if category == 'electronics' %}selected{% endif %}>Electronics</option>
                        <option value="books" {% if category == 'books' %}selected{% endif %}>Books</option>
                        <option value="home-garden" {% if category == 'home-garden' %}selected{% endif %}>Home & Garden</option>
                        <option value="clothing" {% if category == 'clothing' %}selected{% endif %}>Clothing</option>
                        <option value="sports" {% if category == 'sports' %}selected{% endif %}>Sports & Outdoors</option>
                        <option value="toys" {% if category == 'toys' %}selected{% endif %}>Toys & Games</option>
                        <option value="health" {% if category == 'health' %}selected{% endif %}>Health & Beauty</option>
                        <option value="automotive" {% if category == 'automotive' %}selected{% endif %}>Automotive</option>
                        <option value="tools" {% if category == 'tools' %}selected{% endif %}>Tools & Hardware</option>
                        <option value="other" {% if category == 'other' %}selected{% endif %}>Other</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-outline-primary w-100">
                        <i class="bi bi-search"></i> Search
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Products Table -->
    <div class="card">
        <div class="card-body">
            {% if products.items %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Title</th>
                                <th>Category</th>
                                <th>Price</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products.items %}
                            <tr>
                                <td>
                                    {% if product.image_url %}
                                        <img src="{{ product.image_url }}" alt="{{ product.title }}" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                    {% else %}
                                        <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                            <i class="bi bi-image text-muted"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ product.title[:50] }}{% if product.title|length > 50 %}...{% endif %}</strong>
                                    {% if product.description %}
                                        <br><small class="text-muted">{{ product.description[:100] }}{% if product.description|length > 100 %}...{% endif %}</small>
                                    {% endif %}
                                </td>
                                <td><span class="badge bg-secondary">{{ product.category }}</span></td>
                                <td>{{ product.price or '-' }}</td>
                                <td>
                                    {% if product.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                    {% endif %}
                                </td>
                                <td>{{ product.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('admin.edit_product', id=product.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteProduct({{ product.id }})">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if products.pages > 1 %}
                <nav aria-label="Products pagination">
                    <ul class="pagination justify-content-center">
                        {% if products.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.products', page=products.prev_num, search=search, category=category) }}">Previous</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in products.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != products.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.products', page=page_num, search=search, category=category) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if products.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.products', page=products.next_num, search=search, category=category) }}">Next</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-box-seam fs-1 text-muted"></i>
                    <h4 class="mt-3">No products found</h4>
                    <p class="text-muted">{% if search or category %}Try adjusting your search criteria{% else %}Start by adding your first product{% endif %}</p>
                    <a href="{{ url_for('admin.add_product') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add Product
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this product? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function deleteProduct(productId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/admin/products/delete/${productId}`;
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
{% endblock %}
