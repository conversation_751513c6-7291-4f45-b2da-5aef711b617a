{% extends "admin/base_admin.html" %}

{% block title %}Import Products - Admin Panel{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="bi bi-upload"></i> Import Products from CSV</h4>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.csv_file.label(class="form-label") }}
                            {{ form.csv_file(class="form-control") }}
                            {% if form.csv_file.errors %}
                                <div class="text-danger">
                                    {% for error in form.csv_file.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Select a CSV file with product data</small>
                        </div>
                        
                        <div class="d-flex gap-2">
                            {{ form.submit(class="btn btn-success") }}
                            <a href="{{ url_for('admin.products') }}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-file-earmark-text"></i> CSV Format</h5>
                </div>
                <div class="card-body">
                    <p>Your CSV file should contain the following columns:</p>
                    <ul class="list-unstyled">
                        <li><strong>title</strong> <span class="text-danger">*</span> - Product title</li>
                        <li><strong>description</strong> - Product description</li>
                        <li><strong>image_url</strong> - Image URL</li>
                        <li><strong>affiliate_link</strong> <span class="text-danger">*</span> - Amazon affiliate link</li>
                        <li><strong>category</strong> <span class="text-danger">*</span> - Product category</li>
                        <li><strong>price</strong> - Product price</li>
                        <li><strong>is_active</strong> - Active status (true/false)</li>
                    </ul>
                    <p><small class="text-muted"><span class="text-danger">*</span> Required fields</small></p>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="bi bi-download"></i> Sample CSV</h5>
                </div>
                <div class="card-body">
                    <p>Download a sample CSV file to see the correct format:</p>
                    <a href="{{ url_for('static', filename='sample_products.csv') }}" class="btn btn-outline-primary w-100" download>
                        <i class="bi bi-download"></i> Download Sample
                    </a>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="bi bi-exclamation-triangle"></i> Important Notes</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            Make sure affiliate links include your tracking ID
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            Use valid category names from the dropdown
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            Image URLs should be publicly accessible
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            Duplicate products will be imported as separate entries
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
