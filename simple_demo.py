#!/usr/bin/env python3
"""
Simple demo version of Amazon Affiliate Store
This version uses only built-in Python modules for demonstration
"""

import os
import json
import sqlite3
import hashlib
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import html

class AffiliateStoreHandler(BaseHTTPRequestHandler):
    
    def __init__(self, *args, **kwargs):
        self.db_file = 'demo_store.db'
        self.init_database()
        super().__init__(*args, **kwargs)
    
    def init_database(self):
        """Initialize SQLite database"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # Create users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY,
                username TEXT UNIQUE,
                password_hash TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create products table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY,
                title TEXT NOT NULL,
                description TEXT,
                image_url TEXT,
                affiliate_link TEXT NOT NULL,
                category TEXT NOT NULL,
                price TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create default admin user
        admin_hash = hashlib.sha256('admin123'.encode()).hexdigest()
        cursor.execute('INSERT OR IGNORE INTO users (username, password_hash) VALUES (?, ?)', 
                      ('admin', admin_hash))
        
        # Add sample products
        sample_products = [
            ('iPhone 15 Pro Max', 'Latest iPhone with advanced features', 
             'https://via.placeholder.com/300x200', 
             'https://amazon.com/dp/SAMPLE?tag=your-tag', 'electronics', '$1199.99'),
            ('The Great Gatsby', 'Classic American novel', 
             'https://via.placeholder.com/300x200', 
             'https://amazon.com/dp/SAMPLE2?tag=your-tag', 'books', '$12.99'),
            ('Instant Pot Duo', 'Multi-functional pressure cooker', 
             'https://via.placeholder.com/300x200', 
             'https://amazon.com/dp/SAMPLE3?tag=your-tag', 'home-garden', '$79.99')
        ]
        
        for product in sample_products:
            cursor.execute('''INSERT OR IGNORE INTO products 
                            (title, description, image_url, affiliate_link, category, price) 
                            VALUES (?, ?, ?, ?, ?, ?)''', product)
        
        conn.commit()
        conn.close()
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/' or path == '/index':
            self.serve_home_page()
        elif path == '/products':
            self.serve_products_page()
        elif path == '/admin':
            self.serve_admin_login()
        elif path == '/admin/dashboard':
            self.serve_admin_dashboard()
        elif path.startswith('/product/'):
            product_id = path.split('/')[-1]
            self.serve_product_detail(product_id)
        else:
            self.send_error(404)
    
    def do_POST(self):
        """Handle POST requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/admin/login':
            self.handle_admin_login()
        else:
            self.send_error(404)
    
    def serve_home_page(self):
        """Serve the home page"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM products WHERE is_active = 1 ORDER BY created_at DESC LIMIT 6')
        products = cursor.fetchall()
        conn.close()
        
        content = '''
            <div class="hero">
                <h1>Welcome to Our Amazon Affiliate Store</h1>
                <p>Discover amazing products with great deals!</p>
            </div>

            <div class="container">
                <h2>Latest Products</h2>
                <div class="products-grid">
                    {products}
                </div>

                <div class="text-center">
                    <a href="/products" class="btn">View All Products</a>
                </div>
            </div>
        '''.format(products=self.render_products(products))

        html_content = self.get_html_template('Home - Amazon Affiliate Store', content)
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html_content.encode())
    
    def serve_products_page(self):
        """Serve the products page"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM products WHERE is_active = 1 ORDER BY created_at DESC')
        products = cursor.fetchall()
        conn.close()
        
        content = '''
            <div class="container">
                <h1>All Products</h1>
                <div class="products-grid">
                    {products}
                </div>
            </div>
        '''.format(products=self.render_products(products))

        html_content = self.get_html_template('Products - Amazon Affiliate Store', content)
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html_content.encode())
    
    def serve_product_detail(self, product_id):
        """Serve individual product page"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM products WHERE id = ? AND is_active = 1', (product_id,))
            product = cursor.fetchone()
            conn.close()
            
            if not product:
                self.send_error(404)
                return
            
            price_html = '<p class="price">{}</p>'.format(html.escape(product[6])) if product[6] else ''
            content = '''
                <div class="container">
                    <div class="product-detail">
                        <div class="product-image">
                            <img src="{image_url}" alt="{title}" />
                        </div>
                        <div class="product-info">
                            <h1>{title}</h1>
                            <p class="category">Category: {category}</p>
                            {price}
                            <p class="description">{description}</p>
                            <a href="{affiliate_link}" target="_blank" class="btn btn-buy">Buy on Amazon</a>
                        </div>
                    </div>
                </div>
            '''.format(
                image_url=product[3],
                title=html.escape(product[1]),
                category=html.escape(product[5]),
                price=price_html,
                description=html.escape(product[2] or 'No description available.'),
                affiliate_link=product[4]
            )

            title = '{} - Amazon Affiliate Store'.format(product[1])
            html_content = self.get_html_template(title, content)
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(html_content.encode())
            
        except Exception as e:
            self.send_error(500)
    
    def serve_admin_login(self):
        """Serve admin login page"""
        html_content = self.get_html_template('Admin Login', '''
            <div class="container">
                <div class="login-form">
                    <h2>Admin Login</h2>
                    <form method="POST" action="/admin/login">
                        <div class="form-group">
                            <label>Username:</label>
                            <input type="text" name="username" required>
                        </div>
                        <div class="form-group">
                            <label>Password:</label>
                            <input type="password" name="password" required>
                        </div>
                        <button type="submit" class="btn">Login</button>
                    </form>
                    <p><small>Default: username='admin', password='admin123'</small></p>
                </div>
            </div>
        ''')
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html_content.encode())
    
    def serve_admin_dashboard(self):
        """Serve admin dashboard"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM products')
        total_products = cursor.fetchone()[0]
        cursor.execute('SELECT COUNT(*) FROM products WHERE is_active = 1')
        active_products = cursor.fetchone()[0]
        conn.close()
        
        content = '''
            <div class="container">
                <h1>Admin Dashboard</h1>
                <div class="stats">
                    <div class="stat-card">
                        <h3>{total}</h3>
                        <p>Total Products</p>
                    </div>
                    <div class="stat-card">
                        <h3>{active}</h3>
                        <p>Active Products</p>
                    </div>
                </div>
                <p><a href="/">← Back to Store</a></p>
            </div>
        '''.format(total=total_products, active=active_products)

        html_content = self.get_html_template('Admin Dashboard', content)
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html_content.encode())
    
    def handle_admin_login(self):
        """Handle admin login"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length).decode('utf-8')
        params = parse_qs(post_data)
        
        username = params.get('username', [''])[0]
        password = params.get('password', [''])[0]
        
        # Simple authentication check
        if username == 'admin' and password == 'admin123':
            self.send_response(302)
            self.send_header('Location', '/admin/dashboard')
            self.end_headers()
        else:
            self.send_response(302)
            self.send_header('Location', '/admin?error=1')
            self.end_headers()
    
    def render_products(self, products):
        """Render products HTML"""
        html = ''
        for product in products:
            price_html = '<p class="price">{}</p>'.format(html.escape(product[6])) if product[6] else ''
            html += '''
                <div class="product-card">
                    <img src="{image_url}" alt="{title}" />
                    <h3>{title}</h3>
                    <p class="category">{category}</p>
                    {price}
                    <div class="product-actions">
                        <a href="/product/{product_id}" class="btn btn-small">Details</a>
                        <a href="{affiliate_link}" target="_blank" class="btn btn-buy btn-small">Buy on Amazon</a>
                    </div>
                </div>
            '''.format(
                image_url=product[3],
                title=html.escape(product[1]),
                category=html.escape(product[5]),
                price=price_html,
                product_id=product[0],
                affiliate_link=product[4]
            )
        return html
    
    def get_html_template(self, title, content):
        """Get HTML template"""
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>{title}</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                {css}
            </style>
        </head>
        <body>
            <nav>
                <div class="container">
                    <a href="/" class="logo">AffiliateStore</a>
                    <div class="nav-links">
                        <a href="/">Home</a>
                        <a href="/products">Products</a>
                        <a href="/admin">Admin</a>
                    </div>
                </div>
            </nav>
            <main>
                {content}
            </main>
            <footer>
                <div class="container">
                    <p>&copy; 2024 Amazon Affiliate Store Demo</p>
                    <p><small>This site contains affiliate links. We may earn a commission when you make purchases.</small></p>
                </div>
            </footer>
        </body>
        </html>
        '''.format(title=title, content=content, css=self.get_css())
    
    def get_css(self):
        """Get CSS styles"""
        return '''
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
            
            nav { background: #333; color: white; padding: 1rem 0; }
            nav .container { display: flex; justify-content: space-between; align-items: center; }
            .logo { color: white; text-decoration: none; font-size: 1.5rem; font-weight: bold; }
            .nav-links a { color: white; text-decoration: none; margin-left: 20px; }
            .nav-links a:hover { text-decoration: underline; }
            
            .hero { background: #007bff; color: white; text-align: center; padding: 4rem 0; }
            .hero h1 { font-size: 2.5rem; margin-bottom: 1rem; }
            
            main { min-height: 60vh; padding: 2rem 0; }
            
            .products-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin: 2rem 0; }
            .product-card { border: 1px solid #ddd; border-radius: 8px; padding: 1rem; text-align: center; }
            .product-card img { width: 100%; height: 200px; object-fit: cover; border-radius: 4px; }
            .product-card h3 { margin: 1rem 0 0.5rem; }
            .category { color: #666; font-size: 0.9rem; }
            .price { color: #28a745; font-weight: bold; font-size: 1.2rem; margin: 0.5rem 0; }
            .product-actions { margin-top: 1rem; }
            
            .btn { display: inline-block; background: #007bff; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
            .btn:hover { background: #0056b3; }
            .btn-small { padding: 0.5rem 1rem; font-size: 0.9rem; }
            .btn-buy { background: #ff9900; }
            .btn-buy:hover { background: #e68900; }
            
            .text-center { text-align: center; }
            
            .login-form { max-width: 400px; margin: 2rem auto; padding: 2rem; border: 1px solid #ddd; border-radius: 8px; }
            .form-group { margin-bottom: 1rem; }
            .form-group label { display: block; margin-bottom: 0.5rem; }
            .form-group input { width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; }
            
            .product-detail { display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin: 2rem 0; }
            .product-image img { width: 100%; border-radius: 8px; }
            .product-info h1 { margin-bottom: 1rem; }
            .product-info p { margin-bottom: 1rem; }
            
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 2rem 0; }
            .stat-card { background: #f8f9fa; padding: 2rem; text-align: center; border-radius: 8px; }
            .stat-card h3 { font-size: 2rem; color: #007bff; }
            
            footer { background: #333; color: white; text-align: center; padding: 2rem 0; margin-top: 2rem; }
            
            @media (max-width: 768px) {
                .product-detail { grid-template-columns: 1fr; }
                .hero h1 { font-size: 2rem; }
                nav .container { flex-direction: column; gap: 1rem; }
            }
        '''

def run_server():
    """Run the demo server"""
    port = 8000
    server = HTTPServer(('localhost', port), AffiliateStoreHandler)
    
    print("Amazon Affiliate Store Demo Server")
    print("Running on http://localhost:{}".format(port))
    print("Admin Panel: http://localhost:{}/admin".format(port))
    print("Default Admin: username='admin', password='admin123'")
    print("Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped.")
        server.server_close()

if __name__ == '__main__':
    run_server()
