{% extends "admin/base_admin.html" %}

{% block title %}Add Product - Admin Panel{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="bi bi-plus-circle"></i> Add New Product</h4>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.title.label(class="form-label") }}
                            {{ form.title(class="form-control") }}
                            {% if form.title.errors %}
                                <div class="text-danger">
                                    {% for error in form.title.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", rows="4") }}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {% for error in form.description.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.image_url.label(class="form-label") }}
                                    {{ form.image_url(class="form-control") }}
                                    {% if form.image_url.errors %}
                                        <div class="text-danger">
                                            {% for error in form.image_url.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.image_file.label(class="form-label") }}
                                    {{ form.image_file(class="form-control") }}
                                    {% if form.image_file.errors %}
                                        <div class="text-danger">
                                            {% for error in form.image_file.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">Upload an image or provide a URL above</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.affiliate_link.label(class="form-label") }}
                            {{ form.affiliate_link(class="form-control") }}
                            {% if form.affiliate_link.errors %}
                                <div class="text-danger">
                                    {% for error in form.affiliate_link.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Make sure to include your affiliate tag</small>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.category.label(class="form-label") }}
                                    {{ form.category(class="form-select") }}
                                    {% if form.category.errors %}
                                        <div class="text-danger">
                                            {% for error in form.category.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.price.label(class="form-label") }}
                                    {{ form.price(class="form-control", placeholder="e.g., $29.99") }}
                                    {% if form.price.errors %}
                                        <div class="text-danger">
                                            {% for error in form.price.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        
                        <div class="d-flex gap-2">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('admin.products') }}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-info-circle"></i> Tips</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            Use descriptive titles that include key features
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            Write compelling descriptions that highlight benefits
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            Use high-quality product images
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            Ensure affiliate links include your tracking ID
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            Choose appropriate categories for better organization
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="bi bi-link-45deg"></i> Affiliate Link Format</h5>
                </div>
                <div class="card-body">
                    <p class="small">Your Amazon affiliate links should look like:</p>
                    <code class="small">
                        https://amazon.com/dp/PRODUCT_ID?tag=YOUR_TAG
                    </code>
                    <p class="small mt-2">Make sure to replace YOUR_TAG with your actual affiliate tag.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
