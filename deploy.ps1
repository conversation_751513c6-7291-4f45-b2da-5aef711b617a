# PowerShell Deployment Script for Amazon Affiliate Store
# Run this script as Administrator

param(
    [string]$SiteName = "AmazonAffiliateStore",
    [string]$SitePath = "C:\inetpub\wwwroot\augaff",
    [string]$PythonPath = "C:\Python311",
    [string]$DatabaseServer = "localhost",
    [string]$DatabaseName = "AffiliateStore"
)

Write-Host "Starting deployment of Amazon Affiliate Store..." -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "This script must be run as Administrator. Exiting..."
    exit 1
}

# Install IIS and required features
Write-Host "Installing IIS and required features..." -ForegroundColor Yellow
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole, IIS-WebServer, IIS-CommonHttpFeatures, IIS-HttpErrors, IIS-HttpLogging, IIS-RequestFiltering, IIS-StaticContent, IIS-DefaultDocument, IIS-DirectoryBrowsing, IIS-ASPNET45, IIS-NetFxExtensibility45, IIS-ISAPIExtensions, IIS-ISAPIFilter, IIS-HttpCompressionStatic, IIS-HttpCompressionDynamic, IIS-Security, IIS-RequestFiltering, IIS-BasicAuthentication, IIS-WindowsAuthentication, IIS-DigestAuthentication, IIS-ClientCertificateMappingAuthentication, IIS-IISCertificateMappingAuthentication, IIS-URLAuthorization, IIS-IPSecurity, IIS-Performance, IIS-WebServerManagementTools, IIS-ManagementConsole, IIS-IIS6ManagementCompatibility, IIS-Metabase, IIS-CGI -All

# Install Python if not exists
if (-not (Test-Path $PythonPath)) {
    Write-Host "Python not found at $PythonPath. Please install Python 3.11 or later." -ForegroundColor Red
    Write-Host "Download from: https://www.python.org/downloads/" -ForegroundColor Yellow
    exit 1
}

# Create site directory
Write-Host "Creating site directory..." -ForegroundColor Yellow
if (-not (Test-Path $SitePath)) {
    New-Item -ItemType Directory -Path $SitePath -Force
}

# Copy application files
Write-Host "Copying application files..." -ForegroundColor Yellow
Copy-Item -Path ".\*" -Destination $SitePath -Recurse -Force -Exclude @("deploy.ps1", "README.md", ".git*", "__pycache__", "*.pyc")

# Create logs directory
$LogsPath = Join-Path $SitePath "logs"
if (-not (Test-Path $LogsPath)) {
    New-Item -ItemType Directory -Path $LogsPath -Force
}

# Install Python packages
Write-Host "Installing Python packages..." -ForegroundColor Yellow
Set-Location $SitePath
& "$PythonPath\python.exe" -m pip install --upgrade pip
& "$PythonPath\python.exe" -m pip install -r requirements.txt

# Install wfastcgi
Write-Host "Installing and configuring wfastcgi..." -ForegroundColor Yellow
& "$PythonPath\python.exe" -m pip install wfastcgi
& "$PythonPath\Scripts\wfastcgi-enable.exe"

# Create IIS Application Pool
Write-Host "Creating IIS Application Pool..." -ForegroundColor Yellow
Import-Module WebAdministration
if (Get-IISAppPool -Name $SiteName -ErrorAction SilentlyContinue) {
    Remove-IISAppPool -Name $SiteName -Confirm:$false
}
New-IISAppPool -Name $SiteName -Force
Set-ItemProperty -Path "IIS:\AppPools\$SiteName" -Name processModel.identityType -Value ApplicationPoolIdentity
Set-ItemProperty -Path "IIS:\AppPools\$SiteName" -Name recycling.periodicRestart.time -Value "00:00:00"

# Create IIS Website
Write-Host "Creating IIS Website..." -ForegroundColor Yellow
if (Get-IISSite -Name $SiteName -ErrorAction SilentlyContinue) {
    Remove-IISSite -Name $SiteName -Confirm:$false
}
New-IISSite -Name $SiteName -PhysicalPath $SitePath -Port 80 -ApplicationPool $SiteName

# Set permissions
Write-Host "Setting permissions..." -ForegroundColor Yellow
$acl = Get-Acl $SitePath
$accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
$acl.SetAccessRule($accessRule)
$accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IUSR", "ReadAndExecute", "ContainerInherit,ObjectInherit", "None", "Allow")
$acl.SetAccessRule($accessRule)
Set-Acl -Path $SitePath -AclObject $acl

# Create environment file
Write-Host "Creating environment configuration..." -ForegroundColor Yellow
$envContent = @"
# Database Configuration
DATABASE_URL=mssql+pyodbc://username:password@$DatabaseServer/$DatabaseName?driver=ODBC+Driver+17+for+SQL+Server

# Security
SECRET_KEY=$(New-Guid)

# Application Settings
FLASK_ENV=production
FLASK_DEBUG=False

# Amazon Affiliate Settings
AMAZON_AFFILIATE_TAG=your-affiliate-tag-20
"@

$envContent | Out-File -FilePath (Join-Path $SitePath ".env") -Encoding UTF8

Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Update the .env file with your actual database connection string"
Write-Host "2. Update your Amazon affiliate tag in the .env file"
Write-Host "3. Install SQL Server ODBC Driver 17 if not already installed"
Write-Host "4. Create the database and update the connection string"
Write-Host "5. Test the application by visiting http://localhost"
Write-Host ""
Write-Host "Admin login: username='admin', password='admin123'" -ForegroundColor Cyan
Write-Host "Change the admin password after first login!" -ForegroundColor Red
