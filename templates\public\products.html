{% extends "public/base_public.html" %}

{% block title %}All Products - Amazon Affiliate Store{% endblock %}
{% block description %}Browse our complete collection of Amazon products across all categories.{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1>All Products</h1>
            <p class="text-muted">
                {% if search %}
                    Search results for "{{ search }}"
                {% elif current_category %}
                    Products in {{ current_category.replace('-', ' ').title() }}
                {% else %}
                    Browse our complete collection of products
                {% endif %}
            </p>
        </div>
        <div class="col-md-4">
            <!-- Filter Form -->
            <form method="GET" class="d-flex gap-2">
                <select name="category" class="form-select" onchange="this.form.submit()">
                    <option value="">All Categories</option>
                    {% for cat in categories %}
                        <option value="{{ cat }}" {% if cat == current_category %}selected{% endif %}>
                            {{ cat.replace('-', ' ').title() }}
                        </option>
                    {% endfor %}
                </select>
                <input type="hidden" name="search" value="{{ search }}">
            </form>
        </div>
    </div>
    
    <!-- Products Grid -->
    {% if products.items %}
        <div class="row">
            {% for product in products.items %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card h-100 shadow-sm">
                    {% if product.image_url %}
                        <img src="{{ product.image_url }}" class="card-img-top" alt="{{ product.title }}" style="height: 200px; object-fit: cover;">
                    {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="bi bi-image text-muted display-4"></i>
                        </div>
                    {% endif %}
                    
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">{{ product.title[:60] }}{% if product.title|length > 60 %}...{% endif %}</h6>
                        <p class="card-text text-muted small flex-grow-1">
                            {{ product.description[:100] if product.description else 'No description available.' }}
                            {% if product.description and product.description|length > 100 %}...{% endif %}
                        </p>
                        
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge bg-secondary">{{ product.category.replace('-', ' ').title() }}</span>
                                {% if product.price %}
                                    <span class="fw-bold text-success">{{ product.price }}</span>
                                {% endif %}
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="{{ product.affiliate_link }}" class="btn btn-warning" target="_blank" rel="noopener">
                                    <i class="bi bi-cart-plus"></i> Buy on Amazon
                                </a>
                                <a href="{{ url_for('public.product_detail', id=product.id) }}" class="btn btn-outline-primary btn-sm">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if products.pages > 1 %}
        <nav aria-label="Products pagination" class="mt-5">
            <ul class="pagination justify-content-center">
                {% if products.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('public.products', page=products.prev_num, search=search, category=current_category) }}">Previous</a>
                    </li>
                {% endif %}
                
                {% for page_num in products.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != products.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('public.products', page=page_num, search=search, category=current_category) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if products.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('public.products', page=products.next_num, search=search, category=current_category) }}">Next</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <!-- No Products Found -->
        <div class="text-center py-5">
            <i class="bi bi-search display-1 text-muted"></i>
            <h3 class="mt-3">No products found</h3>
            <p class="text-muted">
                {% if search %}
                    Try adjusting your search terms or browse all products.
                {% else %}
                    Check back later for new products.
                {% endif %}
            </p>
            {% if search or current_category %}
                <a href="{{ url_for('public.products') }}" class="btn btn-primary">
                    <i class="bi bi-arrow-left"></i> View All Products
                </a>
            {% endif %}
        </div>
    {% endif %}
</div>
{% endblock %}
