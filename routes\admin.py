from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash
from werkzeug.utils import secure_filename
import os
import pandas as pd
from models import User, Product, db
from forms import LoginForm, ProductForm, SearchForm, CSVUploadForm

admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('admin.dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and check_password_hash(user.password_hash, form.password.data):
            login_user(user, remember=form.remember_me.data)
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('admin.dashboard'))
        flash('Invalid username or password', 'error')
    
    return render_template('admin/login.html', form=form)

@admin_bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out successfully', 'success')
    return redirect(url_for('public.index'))

@admin_bp.route('/')
@admin_bp.route('/dashboard')
@login_required
def dashboard():
    # Get statistics
    total_products = Product.query.count()
    active_products = Product.query.filter_by(is_active=True).count()
    categories = db.session.query(Product.category, db.func.count(Product.id)).group_by(Product.category).all()
    
    # Get recent products
    recent_products = Product.query.order_by(Product.created_at.desc()).limit(5).all()
    
    return render_template('admin/dashboard.html', 
                         total_products=total_products,
                         active_products=active_products,
                         categories=categories,
                         recent_products=recent_products)

@admin_bp.route('/products')
@login_required
def products():
    form = SearchForm()
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category = request.args.get('category', '')
    
    query = Product.query
    
    if search:
        query = query.filter(Product.title.contains(search))
    
    if category:
        query = query.filter_by(category=category)
    
    products = query.order_by(Product.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False)
    
    return render_template('admin/products.html', products=products, form=form, 
                         search=search, category=category)

@admin_bp.route('/products/add', methods=['GET', 'POST'])
@login_required
def add_product():
    form = ProductForm()
    if form.validate_on_submit():
        # Handle image upload
        image_url = form.image_url.data
        if form.image_file.data:
            filename = secure_filename(form.image_file.data.filename)
            if filename:
                filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
                form.image_file.data.save(filepath)
                image_url = url_for('static', filename=f'uploads/{filename}')
        
        product = Product(
            title=form.title.data,
            description=form.description.data,
            image_url=image_url,
            affiliate_link=form.affiliate_link.data,
            category=form.category.data,
            price=form.price.data,
            is_active=form.is_active.data
        )
        
        db.session.add(product)
        db.session.commit()
        flash('Product added successfully!', 'success')
        return redirect(url_for('admin.products'))
    
    return render_template('admin/add_product.html', form=form)

@admin_bp.route('/products/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_product(id):
    product = Product.query.get_or_404(id)
    form = ProductForm(obj=product)
    
    if form.validate_on_submit():
        # Handle image upload
        if form.image_file.data:
            filename = secure_filename(form.image_file.data.filename)
            if filename:
                filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
                form.image_file.data.save(filepath)
                product.image_url = url_for('static', filename=f'uploads/{filename}')
        elif form.image_url.data:
            product.image_url = form.image_url.data
        
        product.title = form.title.data
        product.description = form.description.data
        product.affiliate_link = form.affiliate_link.data
        product.category = form.category.data
        product.price = form.price.data
        product.is_active = form.is_active.data
        
        db.session.commit()
        flash('Product updated successfully!', 'success')
        return redirect(url_for('admin.products'))
    
    return render_template('admin/edit_product.html', form=form, product=product)

@admin_bp.route('/products/delete/<int:id>', methods=['POST'])
@login_required
def delete_product(id):
    product = Product.query.get_or_404(id)
    db.session.delete(product)
    db.session.commit()
    flash('Product deleted successfully!', 'success')
    return redirect(url_for('admin.products'))

@admin_bp.route('/products/import', methods=['GET', 'POST'])
@login_required
def import_products():
    form = CSVUploadForm()
    if form.validate_on_submit():
        try:
            # Save uploaded file
            filename = secure_filename(form.csv_file.data.filename)
            filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
            form.csv_file.data.save(filepath)

            # Read CSV file
            df = pd.read_csv(filepath)

            # Validate required columns
            required_columns = ['title', 'affiliate_link', 'category']
            if not all(col in df.columns for col in required_columns):
                flash(f'CSV must contain columns: {", ".join(required_columns)}', 'error')
                return render_template('admin/import_products.html', form=form)

            # Import products
            imported_count = 0
            for _, row in df.iterrows():
                if pd.notna(row['title']) and pd.notna(row['affiliate_link']):
                    product = Product(
                        title=str(row['title']),
                        description=str(row.get('description', '')),
                        image_url=str(row.get('image_url', '')),
                        affiliate_link=str(row['affiliate_link']),
                        category=str(row['category']),
                        price=str(row.get('price', '')),
                        is_active=bool(row.get('is_active', True))
                    )
                    db.session.add(product)
                    imported_count += 1

            db.session.commit()

            # Clean up uploaded file
            os.remove(filepath)

            flash(f'Successfully imported {imported_count} products!', 'success')
            return redirect(url_for('admin.products'))

        except Exception as e:
            flash(f'Error importing CSV: {str(e)}', 'error')

    return render_template('admin/import_products.html', form=form)
