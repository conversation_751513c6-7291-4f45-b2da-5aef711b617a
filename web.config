<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <handlers>
      <add name="Python FastCGI" path="*" verb="*" modules="FastCgiModule" scriptProcessor="C:\Python311\python.exe|C:\inetpub\wwwroot\augaff\wfastcgi.py" resourceType="Unspecified" requireAccess="Script" />
    </handlers>
    
    <rewrite>
      <rules>
        <rule name="Static Files" stopProcessing="true">
          <match url="^(static/.*|favicon\.ico|robots\.txt)$" />
          <action type="None" />
        </rule>
        <rule name="Flask Application" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="app.py" />
        </rule>
      </rules>
    </rewrite>
    
    <staticContent>
      <mimeMap fileExtension=".json" mimeType="application/json" />
      <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
      <mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />
    </staticContent>
    
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="52428800" />
      </requestFiltering>
    </security>
    
    <httpErrors errorMode="Detailed" />
    
    <defaultDocument>
      <files>
        <clear />
        <add value="app.py" />
      </files>
    </defaultDocument>
  </system.webServer>
  
  <appSettings>
    <add key="WSGI_HANDLER" value="app.app" />
    <add key="PYTHONPATH" value="C:\inetpub\wwwroot\augaff" />
    <add key="WSGI_LOG" value="C:\inetpub\wwwroot\augaff\logs\wfastcgi.log" />
  </appSettings>
</configuration>
