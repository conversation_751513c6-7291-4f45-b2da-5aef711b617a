{% extends "public/base_public.html" %}

{% block title %}{{ product.title }} - Amazon Affiliate Store{% endblock %}
{% block description %}{{ product.description[:160] if product.description else 'Check out this amazing product on Amazon.' }}{% endblock %}
{% block og_title %}{{ product.title }}{% endblock %}
{% block og_description %}{{ product.description[:160] if product.description else 'Check out this amazing product on Amazon.' }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('public.index') }}">Home</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('public.products') }}">Products</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('public.products_by_category', category=product.category) }}">{{ product.category.replace('-', ' ').title() }}</a></li>
            <li class="breadcrumb-item active">{{ product.title[:50] }}{% if product.title|length > 50 %}...{% endif %}</li>
        </ol>
    </nav>
    
    <!-- Product Details -->
    <div class="row">
        <div class="col-md-6">
            {% if product.image_url %}
                <img src="{{ product.image_url }}" class="img-fluid rounded shadow" alt="{{ product.title }}">
            {% else %}
                <div class="bg-light d-flex align-items-center justify-content-center rounded shadow" style="height: 400px;">
                    <i class="bi bi-image text-muted display-1"></i>
                </div>
            {% endif %}
        </div>
        
        <div class="col-md-6">
            <div class="ps-md-4">
                <span class="badge bg-secondary mb-2">{{ product.category.replace('-', ' ').title() }}</span>
                <h1 class="mb-3">{{ product.title }}</h1>
                
                {% if product.price %}
                    <div class="mb-3">
                        <span class="display-6 fw-bold text-success">{{ product.price }}</span>
                    </div>
                {% endif %}
                
                {% if product.description %}
                    <div class="mb-4">
                        <h5>Description</h5>
                        <p class="text-muted">{{ product.description }}</p>
                    </div>
                {% endif %}
                
                <!-- Call to Action -->
                <div class="d-grid gap-2 mb-4">
                    <a href="{{ product.affiliate_link }}" class="btn btn-warning btn-lg" target="_blank" rel="noopener">
                        <i class="bi bi-cart-plus"></i> Buy Now on Amazon
                    </a>
                    <small class="text-muted text-center">
                        <i class="bi bi-shield-check"></i> Secure checkout through Amazon
                    </small>
                </div>
                
                <!-- Product Info -->
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title"><i class="bi bi-info-circle"></i> Product Information</h6>
                        <ul class="list-unstyled mb-0">
                            <li><strong>Category:</strong> {{ product.category.replace('-', ' ').title() }}</li>
                            <li><strong>Added:</strong> {{ product.created_at.strftime('%B %d, %Y') }}</li>
                            {% if product.price %}
                                <li><strong>Price:</strong> {{ product.price }}</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
                
                <!-- Affiliate Disclosure -->
                <div class="alert alert-info mt-3">
                    <small>
                        <i class="bi bi-info-circle"></i>
                        <strong>Affiliate Disclosure:</strong> This page contains affiliate links. We may earn a commission when you click on or make purchases via our affiliate links.
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Related Products -->
    {% if related_products %}
    <section class="mt-5">
        <h3 class="mb-4">Related Products</h3>
        <div class="row">
            {% for related in related_products %}
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card h-100 shadow-sm">
                    {% if related.image_url %}
                        <img src="{{ related.image_url }}" class="card-img-top" alt="{{ related.title }}" style="height: 200px; object-fit: cover;">
                    {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="bi bi-image text-muted display-4"></i>
                        </div>
                    {% endif %}
                    
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">{{ related.title[:50] }}{% if related.title|length > 50 %}...{% endif %}</h6>
                        <p class="card-text text-muted small flex-grow-1">
                            {{ related.description[:80] if related.description else 'No description available.' }}
                            {% if related.description and related.description|length > 80 %}...{% endif %}
                        </p>
                        
                        <div class="mt-auto">
                            {% if related.price %}
                                <div class="text-center mb-2">
                                    <span class="fw-bold text-success">{{ related.price }}</span>
                                </div>
                            {% endif %}
                            
                            <div class="d-grid gap-2">
                                <a href="{{ related.affiliate_link }}" class="btn btn-warning btn-sm" target="_blank" rel="noopener">
                                    <i class="bi bi-cart-plus"></i> Buy on Amazon
                                </a>
                                <a href="{{ url_for('public.product_detail', id=related.id) }}" class="btn btn-outline-primary btn-sm">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}
</div>
{% endblock %}
