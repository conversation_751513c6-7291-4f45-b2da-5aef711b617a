# 📘 Project Best Practices

## 1. Project Purpose
Amazon Affiliate Store is a Flask-based web application providing:
- An admin panel to manage affiliate products (create, update, delete, CSV import)
- A public storefront to browse products, view details, and navigate by category
- Windows/IIS-friendly deployment with FastCGI support

Domain: e-commerce affiliate catalog and content management.


## 2. Project Structure
- app.py — Application configuration, extension initialization, blueprint registration, table creation, default admin bootstrap
- models.py — SQLAlchemy models (User, Product). Note: `db` is injected from app.py (see conventions below)
- forms.py — Flask-WTF forms for login, product management, search, and CSV upload
- routes/
  - admin.py — Admin-facing routes (auth, dashboard, product CRUD, CSV import)
  - public.py — Public-facing routes (home, products list, category, detail, static pages)
- templates/
  - admin/ — Admin Jinja2 templates
  - public/ — Public Jinja2 templates
  - base.html — Base layout
- static/ — Static assets and uploads (ensure `static/uploads` exists and is writable)
- run_dev.py — Development runner (loads .env, sets dev flags)
- wsgi.py — WSGI entrypoint for production
- web.config — IIS/FastCGI configuration for Windows deployments
- requirements.txt — Python dependencies (ensure aligned with README)
- test_app.py — Basic unittest-based smoke tests and CLI harness
- README.md — Setup, deployment, and operational guidance

Entry points:
- Development: `python run_dev.py`
- Direct run: `python app.py`
- WSGI/IIS: `wsgi.py` with web.config

Configuration loading:
- `.env` via `python-dotenv` (loaded in app.py and run_dev.py)
- Key envs: `SECRET_KEY`, `DATABASE_URL`, `FLASK_ENV`, `FLASK_DEBUG`, optional `UPLOAD_FOLDER`, `MAX_CONTENT_LENGTH`


## 3. Test Strategy
Framework: Python `unittest` with Flask test client.
- Location: `test_app.py` (expand to a `tests/` package as the project grows)
- Philosophy: 
  - Smoke tests for key pages and admin auth
  - Add unit tests around forms, model methods (`Product.to_dict`), and route logic
  - Add integration tests for DB flows (CRUD, pagination, filtering)
- CSRF in tests: Prefer disabling CSRF in testing config rather than passing dummy tokens:
  - In tests setup: `app.config['WTF_CSRF_ENABLED'] = False`
- Mocking/stubbing: Use `unittest.mock` for external operations (e.g., file IO, pandas CSV parsing)
- Database strategy:
  - Use SQLite in-memory (`sqlite:///:memory:`) for fast tests
  - Create/drop tables per test case or module
- Coverage expectations:
  - Aim for 70%+ lines, focusing on route branches, CSV import edge cases, and admin workflows

Suggested test layout (future):
- tests/
  - test_public_routes.py
  - test_admin_auth.py
  - test_admin_products.py
  - test_csv_import.py
  - conftest or test utils for app factory and fixtures


## 4. Code Style
Language: Python 3.x (compatible with Windows/IIS deployment).
- Organization
  - Keep blueprints in `routes/` with clear separation (admin vs public)
  - Form classes in `forms.py`, models in `models.py`
- Naming
  - snake_case for functions, variables, and routes
  - PascalCase for classes (forms and models)
  - Templates and static assets use kebab-case or underscores consistently
- Typing & docs
  - Optional type hints for new code; document complex functions with short docstrings
  - Keep view functions concise; extract helpers if logic grows
- Error handling
  - Use `try/except` around IO (file uploads, CSV parse) and DB commits with `flash` for user feedback
  - Log exceptions (see Logging in Other Notes)
- Database interactions
  - Use SQLAlchemy session from `models.db` (injected in app.py)
  - Commit in transactional chunks; prefer `db.session.add_all([...])` for batches
  - Validate and sanitize inputs before persisting
- Request handling
  - Use Flask-WTF validation; rely on `form.validate_on_submit()`
  - Use `secure_filename` for uploads and validate content types
- Templates
  - Extend base layouts; keep logic minimal in templates
  - Use pagination patterns already present (`paginate(..., error_out=False)`)


## 5. Common Patterns
- Blueprints: `admin_bp` and `public_bp` registered in app.py
- Forms: Flask-WTF with validators; `hidden_tag()` for CSRF
- Auth: Flask-Login (`login_manager`, `@login_required`, `current_user`)
- CSRF: `CSRFProtect(app)` enabled globally
- DB access: SQLAlchemy via injected `models.db`; models declare `__tablename__`
- File uploads: `secure_filename`, save under `static/uploads`; URL via `url_for('static', ...)`
- Pagination: `.paginate(page=..., per_page=..., error_out=False)`
- Serialization: `Product.to_dict()` for API-like usages
- Configuration: `.env` with sensible defaults (SQLite for dev, secure envs for prod)

Design/architectural notes:
- The app uses a single-module initialization pattern (not an app factory). Maintain this pattern unless committing to a larger refactor.
- `models.db` is initially `None` and is assigned from `app.db` in `app.py` to avoid circular imports. Preserve this injection approach when adding new models.


## 6. Do's and Don'ts
✅ Do
- Use environment variables for secrets (`SECRET_KEY`) and `DATABASE_URL`
- Keep requirements consistent with code and README; pin compatible versions
- Validate all form inputs and CSV schema; handle missing/invalid fields gracefully
- Ensure `static/uploads` exists and is writeable in deployment
- Use `flash` messages for user feedback after actions
- Protect admin routes with `@login_required` and maintain `login_manager.login_view`
- Handle pagination and query filters defensively (empty search/category)
- Write tests for new routes and forms; disable CSRF in test config
- Run `db.create_all()` in an app context during bootstrap only for development or first-run scenarios

❌ Don't
- Hardcode secrets or credentials in source (avoid default admin in production)
- Trust CSV content blindly (parse booleans and sanitize strings; never eval)
- Write files outside the configured upload directory
- Create new SQLAlchemy instances in models or routes (always use injected `models.db`)
- Swallow exceptions silently; log them and provide user-safe messages
- Mix Windows/IIS deployment specifics into core logic (keep in `web.config`/deployment scripts)


## 7. Tools & Dependencies
Key libraries
- Flask, Flask-SQLAlchemy, Flask-Login, Flask-WTF, WTForms, Werkzeug
- python-dotenv for configuration
- pandas for CSV import (ensure included in requirements)

Setup
- Install: `pip install -r requirements.txt`
- Dev run: `python run_dev.py`
- Tests: `python -m unittest test_app.TestAffiliateStore`
- IIS/FastCGI: Configure via `web.config` and `wfastcgi` (see README)

Configuration
- `.env` examples:
  - `SECRET_KEY=change-me-in-prod`
  - `DATABASE_URL=sqlite:///affiliate_store.db` (dev)
  - `DATABASE_URL=mssql+pyodbc://user:pass@server/db?driver=ODBC+Driver+17+for+SQL+Server` (prod)
  - `UPLOAD_FOLDER=static/uploads`
  - `MAX_CONTENT_LENGTH=16777216` (16MB)

Version alignment
- Keep `requirements.txt` and README in sync (e.g., Flask version, pandas, bcrypt/werkzeug usage)
- The current code uses `werkzeug.security` hashing; if adopting bcrypt, update code and requirements coherently.


## 8. Other Notes
- Security
  - Change the default admin credentials immediately after first run; remove auto-creation in production builds
  - Use HTTPS in production and set secure cookie/session options
- CSV import edge cases
  - Parse booleans robustly: e.g., `str(value).strip().lower() in {"1","true","yes"}`
  - Validate required columns: `title`, `affiliate_link`, `category`; default or skip rows otherwise
- Migrations
  - Consider adding Alembic for schema evolution instead of relying on `db.create_all()`
- Logging
  - Configure Python logging to file/console; capture exceptions in CSV import and admin actions
- Templates/SEO
  - Ensure affiliate disclosures remain present; keep meta tags accurate for SEO
- Windows/IIS
  - Ensure the Python path and FastCGI settings match server install paths
  - Set directory permissions for uploads and logs
- LLM code generation hints
  - When creating new models, import `db` from `models` (do not instantiate SQLAlchemy again)
  - Register new blueprints in `app.py` and keep route separation (admin/public)
  - Use Flask-WTF forms and validators for any new input surfaces
  - Maintain pagination and query patterns already used in routes
