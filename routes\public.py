from flask import Blueprint, render_template, request, abort
from models import Product, db
from sqlalchemy import func

public_bp = Blueprint('public', __name__)

@public_bp.route('/')
def index():
    # Get latest products
    latest_products = Product.query.filter_by(is_active=True).order_by(Product.created_at.desc()).limit(12).all()
    
    # Get categories with product counts
    categories = db.session.query(
        Product.category, 
        func.count(Product.id).label('count')
    ).filter_by(is_active=True).group_by(Product.category).all()
    
    return render_template('public/index.html', 
                         latest_products=latest_products,
                         categories=categories)

@public_bp.route('/products')
def products():
    page = request.args.get('page', 1, type=int)
    category = request.args.get('category', '')
    search = request.args.get('search', '')
    
    query = Product.query.filter_by(is_active=True)
    
    if category:
        query = query.filter_by(category=category)
    
    if search:
        query = query.filter(Product.title.contains(search))
    
    products = query.order_by(Product.created_at.desc()).paginate(
        page=page, per_page=12, error_out=False)
    
    # Get all categories for filter
    categories = db.session.query(Product.category).filter_by(is_active=True).distinct().all()
    categories = [cat[0] for cat in categories]
    
    return render_template('public/products.html', 
                         products=products,
                         categories=categories,
                         current_category=category,
                         search=search)

@public_bp.route('/products/<category>')
def products_by_category(category):
    page = request.args.get('page', 1, type=int)
    
    products = Product.query.filter_by(
        category=category, 
        is_active=True
    ).order_by(Product.created_at.desc()).paginate(
        page=page, per_page=12, error_out=False)
    
    if not products.items and page == 1:
        abort(404)
    
    # Get all categories for navigation
    categories = db.session.query(Product.category).filter_by(is_active=True).distinct().all()
    categories = [cat[0] for cat in categories]
    
    return render_template('public/category.html', 
                         products=products,
                         categories=categories,
                         current_category=category)

@public_bp.route('/product/<int:id>')
def product_detail(id):
    product = Product.query.filter_by(id=id, is_active=True).first_or_404()
    
    # Get related products from same category
    related_products = Product.query.filter(
        Product.category == product.category,
        Product.id != product.id,
        Product.is_active == True
    ).limit(4).all()
    
    return render_template('public/product_detail.html', 
                         product=product,
                         related_products=related_products)

@public_bp.route('/about')
def about():
    return render_template('public/about.html')

@public_bp.route('/contact')
def contact():
    return render_template('public/contact.html')

@public_bp.route('/privacy')
def privacy():
    return render_template('public/privacy.html')

@public_bp.route('/terms')
def terms():
    return render_template('public/terms.html')
