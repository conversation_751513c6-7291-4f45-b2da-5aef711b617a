{% extends "public/base_public.html" %}

{% block title %}Amazon Affiliate Store - Best Products & Deals{% endblock %}
{% block description %}Discover the best products on Amazon with our curated selection. Find great deals on electronics, books, home goods, and more.{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-3">Find Amazing Products on Amazon</h1>
                <p class="lead mb-4">Discover our curated selection of the best products across all categories. From electronics to home goods, we've got you covered.</p>
                <a href="{{ url_for('public.products') }}" class="btn btn-light btn-lg">
                    <i class="bi bi-search"></i> Browse All Products
                </a>
            </div>
            <div class="col-lg-6 text-center">
                <i class="bi bi-shop display-1"></i>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
{% if categories %}
<section class="py-5">
    <div class="container">
        <h2 class="text-center mb-5">Shop by Category</h2>
        <div class="row">
            {% for category, count in categories %}
            <div class="col-md-3 col-sm-6 mb-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body">
                        <div class="mb-3">
                            {% if category == 'electronics' %}
                                <i class="bi bi-laptop display-4 text-primary"></i>
                            {% elif category == 'books' %}
                                <i class="bi bi-book display-4 text-success"></i>
                            {% elif category == 'home-garden' %}
                                <i class="bi bi-house display-4 text-warning"></i>
                            {% elif category == 'clothing' %}
                                <i class="bi bi-bag display-4 text-info"></i>
                            {% elif category == 'sports' %}
                                <i class="bi bi-bicycle display-4 text-danger"></i>
                            {% elif category == 'toys' %}
                                <i class="bi bi-controller display-4 text-purple"></i>
                            {% elif category == 'health' %}
                                <i class="bi bi-heart-pulse display-4 text-pink"></i>
                            {% elif category == 'automotive' %}
                                <i class="bi bi-car-front display-4 text-dark"></i>
                            {% elif category == 'tools' %}
                                <i class="bi bi-tools display-4 text-secondary"></i>
                            {% else %}
                                <i class="bi bi-grid display-4 text-muted"></i>
                            {% endif %}
                        </div>
                        <h5 class="card-title">{{ category.replace('-', ' ').title() }}</h5>
                        <p class="text-muted">{{ count }} products</p>
                        <a href="{{ url_for('public.products_by_category', category=category) }}" class="btn btn-outline-primary">
                            Browse {{ category.replace('-', ' ').title() }}
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Latest Products Section -->
{% if latest_products %}
<section class="py-5 bg-light">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-5">
            <h2>Latest Products</h2>
            <a href="{{ url_for('public.products') }}" class="btn btn-outline-primary">
                View All Products <i class="bi bi-arrow-right"></i>
            </a>
        </div>
        
        <div class="row">
            {% for product in latest_products %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card h-100 shadow-sm">
                    {% if product.image_url %}
                        <img src="{{ product.image_url }}" class="card-img-top" alt="{{ product.title }}" style="height: 200px; object-fit: cover;">
                    {% else %}
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="bi bi-image text-muted display-4"></i>
                        </div>
                    {% endif %}
                    
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">{{ product.title[:60] }}{% if product.title|length > 60 %}...{% endif %}</h6>
                        <p class="card-text text-muted small flex-grow-1">
                            {{ product.description[:100] if product.description else 'No description available.' }}
                            {% if product.description and product.description|length > 100 %}...{% endif %}
                        </p>
                        
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge bg-secondary">{{ product.category.replace('-', ' ').title() }}</span>
                                {% if product.price %}
                                    <span class="fw-bold text-success">{{ product.price }}</span>
                                {% endif %}
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="{{ product.affiliate_link }}" class="btn btn-warning" target="_blank" rel="noopener">
                                    <i class="bi bi-cart-plus"></i> Buy on Amazon
                                </a>
                                <a href="{{ url_for('public.product_detail', id=product.id) }}" class="btn btn-outline-primary btn-sm">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <h2 class="text-center mb-5">Why Choose Our Store?</h2>
        <div class="row">
            <div class="col-md-4 text-center mb-4">
                <div class="mb-3">
                    <i class="bi bi-shield-check display-4 text-success"></i>
                </div>
                <h5>Trusted Products</h5>
                <p class="text-muted">All products are carefully selected and reviewed for quality and value.</p>
            </div>
            <div class="col-md-4 text-center mb-4">
                <div class="mb-3">
                    <i class="bi bi-lightning display-4 text-warning"></i>
                </div>
                <h5>Fast Delivery</h5>
                <p class="text-muted">Enjoy Amazon's fast and reliable delivery service on all purchases.</p>
            </div>
            <div class="col-md-4 text-center mb-4">
                <div class="mb-3">
                    <i class="bi bi-star display-4 text-primary"></i>
                </div>
                <h5>Best Deals</h5>
                <p class="text-muted">We constantly monitor prices to bring you the best deals available.</p>
            </div>
        </div>
    </div>
</section>
{% endblock %}
