#!/usr/bin/env python3
"""
Simple test script for Amazon Affiliate Store
Run this to verify the application is working correctly
"""

import os
import sys
import tempfile
import unittest
from unittest.mock import patch

# Add the application directory to Python path
sys.path.insert(0, os.path.dirname(__file__))

class TestAffiliateStore(unittest.TestCase):
    
    def setUp(self):
        """Set up test environment"""
        os.environ['DATABASE_URL'] = 'sqlite:///:memory:'
        os.environ['SECRET_KEY'] = 'test-secret-key'
        os.environ['FLASK_ENV'] = 'testing'
        
        from app import app, db
        self.app = app
        self.client = app.test_client()
        self.app_context = app.app_context()
        self.app_context.push()
        
        # Create tables
        db.create_all()
    
    def tearDown(self):
        """Clean up after tests"""
        from app import db
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_home_page(self):
        """Test that home page loads"""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Amazon Affiliate Store', response.data)
    
    def test_admin_login_page(self):
        """Test that admin login page loads"""
        response = self.client.get('/admin/login')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Admin Login', response.data)
    
    def test_admin_login(self):
        """Test admin login functionality"""
        response = self.client.post('/admin/login', data={
            'username': 'admin',
            'password': 'admin123',
            'csrf_token': self._get_csrf_token('/admin/login')
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200)
    
    def test_products_page(self):
        """Test that products page loads"""
        response = self.client.get('/products')
        self.assertEqual(response.status_code, 200)
    
    def test_about_page(self):
        """Test that about page loads"""
        response = self.client.get('/about')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'About Our Store', response.data)
    
    def test_contact_page(self):
        """Test that contact page loads"""
        response = self.client.get('/contact')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Contact Us', response.data)
    
    def _get_csrf_token(self, url):
        """Helper method to get CSRF token"""
        response = self.client.get(url)
        # In a real test, you'd parse the HTML to get the CSRF token
        # For simplicity, we'll return a dummy token
        return 'dummy-csrf-token'

def run_basic_tests():
    """Run basic functionality tests"""
    print("Running basic tests...")

    try:
        # Test imports
        print("+ Testing imports...")
        import app
        import models
        import forms
        print("+ All imports successful")

        # Test Flask app creation
        print("+ Testing Flask app...")
        from app import app
        assert app is not None
        print("+ Flask app created successfully")

        # Test database models
        print("+ Testing database models...")
        from models import User, Product
        assert User is not None
        assert Product is not None
        print("+ Database models loaded successfully")

        # Test forms
        print("+ Testing forms...")
        from forms import LoginForm, ProductForm
        assert LoginForm is not None
        assert ProductForm is not None
        print("+ Forms loaded successfully")

        print("\nAll basic tests passed!")
        return True

    except Exception as e:
        print(f"\nTest failed: {str(e)}")
        return False

def check_requirements():
    """Check if all required packages are installed"""
    print("Checking requirements...")

    required_packages = [
        'flask', 'flask_sqlalchemy', 'flask_login', 'flask_wtf',
        'wtforms', 'werkzeug', 'bcrypt', 'pyodbc', 'python-dotenv',
        'pillow', 'pandas', 'openpyxl'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"+ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"- {package} - MISSING")

    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    else:
        print("\nAll required packages are installed!")
        return True

def main():
    """Main test function"""
    print("Amazon Affiliate Store - Test Suite")
    print("=" * 50)

    # Check requirements
    if not check_requirements():
        sys.exit(1)

    print()

    # Run basic tests
    if not run_basic_tests():
        sys.exit(1)

    print()
    print("To run full unit tests:")
    print("python -m unittest test_app.TestAffiliateStore")

    print()
    print("To start the development server:")
    print("python app.py")

    print()
    print("Check README.md for deployment instructions")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'unittest':
        # Run unit tests
        unittest.main(argv=[''], exit=False)
    else:
        # Run basic tests
        main()
