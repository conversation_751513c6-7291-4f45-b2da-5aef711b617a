#!/usr/bin/env python3
"""
Development server runner for Amazon Affiliate Store
Use this for local development and testing
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set development environment
os.environ['FLASK_ENV'] = 'development'
os.environ['FLASK_DEBUG'] = 'True'

# Use SQLite for development if no database URL is set
if not os.environ.get('DATABASE_URL'):
    os.environ['DATABASE_URL'] = 'sqlite:///affiliate_store.db'

try:
    from app import app
    
    print("Starting Amazon Affiliate Store Development Server")
    print("=" * 55)
    print(f"URL: http://localhost:5000")
    print(f"Admin Panel: http://localhost:5000/admin")
    print(f"Default Admin: username='admin', password='admin123'")
    print("=" * 55)
    print("Press Ctrl+C to stop the server")
    print()
    
    # Run the development server
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=True
    )
    
except ImportError as e:
    print(f"Import Error: {e}")
    print("Make sure all dependencies are installed:")
    print("   pip install -r requirements.txt")
    sys.exit(1)

except Exception as e:
    print(f"Error starting server: {e}")
    sys.exit(1)
