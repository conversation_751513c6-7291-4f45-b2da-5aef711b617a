# Amazon Affiliate Store

A complete Amazon Affiliate web application built with Flask (Python) that can be hosted on Windows Server with IIS. This application provides a full-featured admin panel for managing products and a responsive public-facing store.

## 🚀 Features

### Admin Panel
- **Secure Login**: Username/password authentication with bcrypt hashing
- **Product Management**: Add, edit, delete products with image upload support
- **Search & Filter**: Find products quickly with search and category filters
- **CSV Import**: Bulk import products from CSV files
- **Dashboard**: Statistics and recent products overview
- **Responsive Design**: Works on desktop and mobile devices

### Public Store
- **Product Catalog**: Browse products by category or search
- **Product Details**: Detailed product pages with affiliate links
- **Mobile Responsive**: Bootstrap-based responsive design
- **SEO Optimized**: Meta tags, structured data, and affiliate compliance
- **Category Navigation**: Easy browsing by product categories

### Technical Features
- **Windows Server Compatible**: Designed for IIS deployment
- **MSSQL Database**: Uses SQL Server with SQLAlchemy ORM
- **Security**: CSRF protection, secure sessions, password hashing
- **File Upload**: Image upload with validation and security
- **Affiliate Compliance**: Proper disclosure and affiliate link handling

## 📋 Requirements

### System Requirements
- Windows Server 2019+ (or Windows 10/11 for development)
- Python 3.11 or later
- SQL Server 2019+ (Express edition is sufficient)
- IIS with CGI support
- ODBC Driver 17 for SQL Server

### Python Dependencies
All dependencies are listed in `requirements.txt`:
- Flask 2.3.3
- Flask-SQLAlchemy 3.0.5
- Flask-Login 0.6.3
- Flask-WTF 1.1.1
- bcrypt 4.0.1
- pyodbc 4.0.39
- pandas 2.1.1 (for CSV import)
- And more...

## 🛠️ Installation & Setup

### Option 1: Automated Deployment (Recommended)

1. **Download and extract** the application files to a temporary directory
2. **Open PowerShell as Administrator**
3. **Navigate** to the application directory
4. **Run the deployment script**:
   ```powershell
   .\deploy.ps1
   ```
5. **Follow the prompts** and wait for completion

### Option 2: Manual Installation

#### Step 1: Install Prerequisites

1. **Install Python 3.11+**
   - Download from [python.org](https://www.python.org/downloads/)
   - Add Python to PATH during installation

2. **Install SQL Server**
   - Download SQL Server Express from Microsoft
   - Install with default settings
   - Note the server name (usually `localhost` or `.\SQLEXPRESS`)

3. **Install ODBC Driver 17**
   - Download from [Microsoft](https://docs.microsoft.com/en-us/sql/connect/odbc/download-odbc-driver-for-sql-server)

4. **Enable IIS with CGI**
   ```powershell
   Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole, IIS-CGI
   ```

#### Step 2: Setup Application

1. **Copy files** to `C:\inetpub\wwwroot\augaff\`

2. **Install Python packages**:
   ```cmd
   cd C:\inetpub\wwwroot\augaff
   pip install -r requirements.txt
   pip install wfastcgi
   wfastcgi-enable
   ```

3. **Create database**:
   ```sql
   CREATE DATABASE AffiliateStore;
   ```

4. **Configure environment**:
   - Copy `.env.example` to `.env`
   - Update database connection string
   - Set your Amazon affiliate tag
   - Generate a secure secret key

#### Step 3: Configure IIS

1. **Create Application Pool**:
   - Open IIS Manager
   - Create new Application Pool named "AffiliateStore"
   - Set .NET CLR version to "No Managed Code"

2. **Create Website**:
   - Add new website
   - Set physical path to `C:\inetpub\wwwroot\augaff`
   - Bind to port 80 (or your preferred port)
   - Assign to "AffiliateStore" application pool

3. **Configure Handler Mapping**:
   - Add script map for `*` to Python FastCGI
   - Executable: `C:\Python311\python.exe|C:\inetpub\wwwroot\augaff\wfastcgi.py`

## ⚙️ Configuration

### Database Configuration

Update the `.env` file with your database details:

```env
DATABASE_URL=mssql+pyodbc://username:password@server/database?driver=ODBC+Driver+17+for+SQL+Server
```

### Amazon Affiliate Configuration

Set your Amazon affiliate tag:

```env
AMAZON_AFFILIATE_TAG=your-affiliate-tag-20
```

### Security Configuration

Generate a secure secret key:

```env
SECRET_KEY=your-very-secure-secret-key-here
```

## 👤 Default Admin Account

- **Username**: `admin`
- **Password**: `admin123`

**⚠️ Important**: Change the default password immediately after first login!

## 📊 CSV Import Format

The CSV import feature expects the following columns:

| Column | Required | Description |
|--------|----------|-------------|
| title | Yes | Product title |
| description | No | Product description |
| image_url | No | Product image URL |
| affiliate_link | Yes | Amazon affiliate link |
| category | Yes | Product category |
| price | No | Product price |
| is_active | No | Active status (true/false) |

### Sample CSV:
```csv
title,description,image_url,affiliate_link,category,price,is_active
"iPhone 15 Pro","Latest iPhone","https://example.com/image.jpg","https://amazon.com/dp/PRODUCT?tag=your-tag","electronics","$999",true
```

## 🔧 Troubleshooting

### Common Issues

1. **500 Internal Server Error**
   - Check Python path in web.config
   - Verify wfastcgi is installed and enabled
   - Check application logs in `logs/` directory

2. **Database Connection Error**
   - Verify SQL Server is running
   - Check connection string in .env
   - Ensure ODBC Driver 17 is installed

3. **Permission Errors**
   - Ensure IIS_IUSRS has full control over application directory
   - Check that uploads directory is writable

4. **Module Import Errors**
   - Verify all packages are installed: `pip list`
   - Check Python path in web.config

### Log Files

- **Application logs**: `logs/app.log`
- **IIS logs**: `C:\inetpub\logs\LogFiles\`
- **FastCGI logs**: `logs/wfastcgi.log`

## 🚀 Production Deployment

### Security Checklist

- [ ] Change default admin password
- [ ] Set strong SECRET_KEY
- [ ] Use HTTPS (SSL certificate)
- [ ] Regular database backups
- [ ] Update dependencies regularly
- [ ] Monitor log files
- [ ] Restrict admin panel access by IP (optional)

### Performance Optimization

- Enable IIS compression
- Configure static file caching
- Use CDN for images
- Regular database maintenance
- Monitor server resources

## 📁 Project Structure

```
augaff/
├── app.py                 # Main Flask application
├── models.py              # Database models
├── forms.py               # WTForms definitions
├── requirements.txt       # Python dependencies
├── web.config            # IIS configuration
├── wsgi.py               # WSGI entry point
├── deploy.ps1            # Deployment script
├── .env                  # Environment variables
├── routes/
│   ├── admin.py          # Admin panel routes
│   └── public.py         # Public routes
├── templates/
│   ├── admin/            # Admin templates
│   └── public/           # Public templates
├── static/
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript files
│   ├── images/           # Static images
│   └── uploads/          # Uploaded files
└── logs/                 # Application logs
```

## 🤝 Support

For support and questions:

1. Check the troubleshooting section
2. Review log files for errors
3. Ensure all requirements are met
4. Verify configuration settings

## 📄 License

This project is provided as-is for educational and commercial use. Please ensure compliance with Amazon's affiliate program terms and conditions.

## 🔗 Amazon Affiliate Compliance

This application includes proper affiliate disclosures as required by Amazon's Operating Agreement and FTC guidelines. All affiliate links are clearly marked, and disclosure statements are included on relevant pages.

---

**Note**: This application is designed for Windows Server deployment. For Linux deployment, you would need to modify the deployment scripts and use a different web server like Nginx or Apache.
