/* Custom CSS for Amazon Affiliate Store */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

/* Card Hover Effects */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

/* Product Cards */
.product-card {
    height: 100%;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.product-card .card-img-top {
    transition: transform 0.3s ease;
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

/* But<PERSON> Styles */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-warning {
    background-color: #ff9900;
    border-color: #ff9900;
    color: white;
}

.btn-warning:hover {
    background-color: #e68900;
    border-color: #e68900;
    color: white;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    color: white;
}

/* Category Icons */
.category-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* Admin Panel Styles */
.admin-sidebar {
    background-color: var(--dark-color);
    min-height: 100vh;
}

.admin-content {
    background-color: var(--light-color);
    min-height: 100vh;
}

/* Statistics Cards */
.stat-card {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Form Styles */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Table Styles */
.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
}

/* Pagination */
.pagination .page-link {
    color: var(--primary-color);
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Footer */
footer {
    background-color: var(--dark-color) !important;
}

footer a {
    transition: color 0.2s ease;
}

footer a:hover {
    color: var(--primary-color) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .lead {
        font-size: 1rem;
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    border: none;
}

/* Badge Styles */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
}

/* Image Styles */
.img-thumbnail {
    border-radius: 8px;
}

/* Search Form */
.search-form .form-control {
    border-radius: 25px 0 0 25px;
}

.search-form .btn {
    border-radius: 0 25px 25px 0;
}

/* Product Detail Page */
.product-detail-image {
    max-height: 500px;
    object-fit: contain;
}

/* Category Page */
.category-header {
    background: linear-gradient(135deg, var(--light-color) 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
}

/* Breadcrumb */
.breadcrumb {
    background-color: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: var(--secondary-color);
}

/* Custom Utilities */
.text-purple {
    color: #6f42c1 !important;
}

.text-pink {
    color: #d63384 !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Print Styles */
@media print {
    .navbar,
    .footer,
    .btn,
    .pagination {
        display: none !important;
    }
}
