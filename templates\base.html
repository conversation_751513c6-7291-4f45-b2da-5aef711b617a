<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Amazon Affiliate Store{% endblock %}</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="{% block description %}Find the best products on Amazon with our curated affiliate store{% endblock %}">
    <meta name="keywords" content="{% block keywords %}amazon, affiliate, products, deals, shopping{% endblock %}">
    <meta name="author" content="Your Affiliate Store">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{% block og_title %}Amazon Affiliate Store{% endblock %}">
    <meta property="og:description" content="{% block og_description %}Find the best products on Amazon{% endblock %}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.url }}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    {% block navbar %}{% endblock %}
    
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    {% block footer %}{% endblock %}
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>
