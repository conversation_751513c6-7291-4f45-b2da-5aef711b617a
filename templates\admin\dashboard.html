{% extends "admin/base_admin.html" %}

{% block title %}Dashboard - Admin Panel{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <h1 class="mb-4">Dashboard</h1>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_products }}</h4>
                            <p class="mb-0">Total Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-box-seam fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ active_products }}</h4>
                            <p class="mb-0">Active Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ categories|length }}</h4>
                            <p class="mb-0">Categories</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-tags fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_products - active_products }}</h4>
                            <p class="mb-0">Inactive Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-x-circle fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Recent Products -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-clock-history"></i> Recent Products</h5>
                </div>
                <div class="card-body">
                    {% if recent_products %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for product in recent_products %}
                                    <tr>
                                        <td>{{ product.title[:50] }}{% if product.title|length > 50 %}...{% endif %}</td>
                                        <td><span class="badge bg-secondary">{{ product.category }}</span></td>
                                        <td>
                                            {% if product.is_active %}
                                                <span class="badge bg-success">Active</span>
                                            {% else %}
                                                <span class="badge bg-danger">Inactive</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ product.created_at.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            <a href="{{ url_for('admin.edit_product', id=product.id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">No products found. <a href="{{ url_for('admin.add_product') }}">Add your first product</a></p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Categories -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-pie-chart"></i> Categories</h5>
                </div>
                <div class="card-body">
                    {% if categories %}
                        {% for category, count in categories %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>{{ category.title() }}</span>
                            <span class="badge bg-primary">{{ count }}</span>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No categories found.</p>
                    {% endif %}
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="bi bi-lightning"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('admin.add_product') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Add Product
                        </a>
                        <a href="{{ url_for('admin.import_products') }}" class="btn btn-success">
                            <i class="bi bi-upload"></i> Import CSV
                        </a>
                        <a href="{{ url_for('public.index') }}" class="btn btn-outline-secondary" target="_blank">
                            <i class="bi bi-eye"></i> View Store
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
