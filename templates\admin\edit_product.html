{% extends "admin/base_admin.html" %}

{% block title %}Edit Product - Admin Panel{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="bi bi-pencil"></i> Edit Product</h4>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.title.label(class="form-label") }}
                            {{ form.title(class="form-control") }}
                            {% if form.title.errors %}
                                <div class="text-danger">
                                    {% for error in form.title.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", rows="4") }}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {% for error in form.description.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.image_url.label(class="form-label") }}
                                    {{ form.image_url(class="form-control") }}
                                    {% if form.image_url.errors %}
                                        <div class="text-danger">
                                            {% for error in form.image_url.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.image_file.label(class="form-label") }}
                                    {{ form.image_file(class="form-control") }}
                                    {% if form.image_file.errors %}
                                        <div class="text-danger">
                                            {% for error in form.image_file.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="form-text text-muted">Upload new image or update URL above</small>
                                </div>
                            </div>
                        </div>
                        
                        {% if product.image_url %}
                        <div class="mb-3">
                            <label class="form-label">Current Image</label>
                            <div>
                                <img src="{{ product.image_url }}" alt="{{ product.title }}" class="img-thumbnail" style="max-width: 200px;">
                            </div>
                        </div>
                        {% endif %}
                        
                        <div class="mb-3">
                            {{ form.affiliate_link.label(class="form-label") }}
                            {{ form.affiliate_link(class="form-control") }}
                            {% if form.affiliate_link.errors %}
                                <div class="text-danger">
                                    {% for error in form.affiliate_link.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.category.label(class="form-label") }}
                                    {{ form.category(class="form-select") }}
                                    {% if form.category.errors %}
                                        <div class="text-danger">
                                            {% for error in form.category.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.price.label(class="form-label") }}
                                    {{ form.price(class="form-control") }}
                                    {% if form.price.errors %}
                                        <div class="text-danger">
                                            {% for error in form.price.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        
                        <div class="d-flex gap-2">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('admin.products') }}" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-info-circle"></i> Product Info</h5>
                </div>
                <div class="card-body">
                    <p><strong>Created:</strong> {{ product.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    <p><strong>Last Updated:</strong> {{ product.updated_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    <p><strong>Status:</strong> 
                        {% if product.is_active %}
                            <span class="badge bg-success">Active</span>
                        {% else %}
                            <span class="badge bg-danger">Inactive</span>
                        {% endif %}
                    </p>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="bi bi-eye"></i> Preview</h5>
                </div>
                <div class="card-body">
                    <a href="{{ url_for('public.product_detail', id=product.id) }}" class="btn btn-outline-primary w-100" target="_blank">
                        <i class="bi bi-box-arrow-up-right"></i> View on Site
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
