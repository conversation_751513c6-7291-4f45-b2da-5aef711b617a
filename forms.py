from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON><PERSON>, FileAllowed
from wtforms import <PERSON><PERSON>ield, TextAreaField, SelectField, PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, Optional

# For older WTForms versions, URL validator might not be available
try:
    from wtforms.validators import URL
except ImportError:
    # Fallback for older versions
    class URL:
        def __init__(self, message=None):
            self.message = message
        def __call__(self, form, field):
            pass

class LoginForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=3, max=80)])
    password = PasswordField('Password', validators=[DataRequired()])
    remember_me = BooleanField('Remember Me')
    submit = SubmitField('Login')

class ProductForm(FlaskForm):
    title = StringField('Product Title', validators=[DataRequired(), Length(min=1, max=200)])
    description = TextAreaField('Description', validators=[Optional()])
    image_url = StringField('Image URL', validators=[Optional(), URL()])
    image_file = FileField('Or Upload Image', validators=[
        FileAllowed(['jpg', 'jpeg', 'png', 'gif'], 'Images only!')
    ])
    affiliate_link = StringField('Amazon Affiliate Link', validators=[DataRequired(), URL()])
    category = SelectField('Category', choices=[
        ('electronics', 'Electronics'),
        ('books', 'Books'),
        ('home-garden', 'Home & Garden'),
        ('clothing', 'Clothing'),
        ('sports', 'Sports & Outdoors'),
        ('toys', 'Toys & Games'),
        ('health', 'Health & Beauty'),
        ('automotive', 'Automotive'),
        ('tools', 'Tools & Hardware'),
        ('other', 'Other')
    ], validators=[DataRequired()])
    price = StringField('Price (Optional)', validators=[Optional(), Length(max=50)])
    is_active = BooleanField('Active', default=True)
    submit = SubmitField('Save Product')

class SearchForm(FlaskForm):
    search = StringField('Search Products', validators=[Optional()])
    category = SelectField('Category', choices=[
        ('', 'All Categories'),
        ('electronics', 'Electronics'),
        ('books', 'Books'),
        ('home-garden', 'Home & Garden'),
        ('clothing', 'Clothing'),
        ('sports', 'Sports & Outdoors'),
        ('toys', 'Toys & Games'),
        ('health', 'Health & Beauty'),
        ('automotive', 'Automotive'),
        ('tools', 'Tools & Hardware'),
        ('other', 'Other')
    ], validators=[Optional()])
    submit = SubmitField('Search')

class CSVUploadForm(FlaskForm):
    csv_file = FileField('CSV File', validators=[
        DataRequired(),
        FileAllowed(['csv'], 'CSV files only!')
    ])
    submit = SubmitField('Upload CSV')
